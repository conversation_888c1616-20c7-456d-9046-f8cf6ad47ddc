const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

/**
 * Model lịch làm việc của cán bộ
 * Quản lý lịch làm việc theo ca sáng (8h) và ca chiều (14h)
 */
const WorkScheduleSchema = new mongoose.Schema(
  {
    // Cán bộ được phân công
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },

    // Ngày làm việc (format: DD-MM-YYYY)
    date: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Kiểm tra định dạng DD-MM-YYYY
          return /^\d{2}-\d{2}-\d{4}$/.test(v);
        },
        message: 'Ngày phải có định dạng DD-MM-YYYY'
      }
    },

    // Danh sách ca làm việc trong ngày
    shifts: [{
      // Loại ca: 'morning' (8h) hoặc 'afternoon' (14h)
      type: {
        type: String,
        enum: ['morning', 'afternoon'],
        required: true
      },

      // Giờ bắt đầu ca làm việc
      startTime: {
        type: String,
        required: true // '08:00' hoặc '14:00'
      },

      // Trạng thái ca làm việc
      status: {
        type: String,
        enum: ['scheduled', 'completed', 'missed', 'excused'],
        default: 'scheduled'
      },

      // Thời gian điểm danh thực tế (timestamp)
      checkinTime: {
        type: Number
      },

      // Vị trí điểm danh (optional)
      location: {
        lat: {
          type: Number
        },
        lng: {
          type: Number
        },
        address: {
          type: String
        }
      }
    }],

    // Người tạo lịch làm việc
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },

    // Metadata
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    status: {
      type: Number,
      default: 1 // 1: active, 0: inactive
    }
  },
  { id: false, versionKey: false }
)

// Indexes cho performance
WorkScheduleSchema.index({ user: 1, date: 1 }) // Tìm lịch theo user và ngày
WorkScheduleSchema.index({ date: 1, status: 1 }) // Tìm lịch theo ngày
WorkScheduleSchema.index({ createdBy: 1, createdAt: -1 }) // Lịch sử tạo lịch
WorkScheduleSchema.index({ user: 1, createdAt: -1 }) // Lịch sử của user

module.exports = mongoConnections("master").model("WorkSchedule", WorkScheduleSchema)