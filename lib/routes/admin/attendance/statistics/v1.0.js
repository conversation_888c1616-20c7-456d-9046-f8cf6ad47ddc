const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const attendanceService = require('../../../../services/attendanceService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API thống kê chuyên cần
 * POST /api/v1.0/attendance/statistics
 */
module.exports = (req, res) => {
  const viewerId = req.user.id;
  const {
    userId,
    unitId,
    startDate,
    endDate
  } = req.body;

  const validateParams = (next) => {
    const schema = Joi.object({
      userId: Joi.objectId().optional(),
      unitId: Joi.objectId().optional(),
      startDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required(),
      endDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra startDate <= endDate
    if (new Date(startDate) > new Date(endDate)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.ATTENDANCE.WRONG_DATE
      });
    }

    next();
  };

  const getStatistics = (next) => {
    try {
      attendanceService.getAttendanceStatisticsAdmin(
        viewerId,
        userId,
        unitId,
        startDate,
        endDate
      )
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: res.message,
            data: res.data
          });
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  async.waterfall([
    validateParams,
    getStatistics
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};