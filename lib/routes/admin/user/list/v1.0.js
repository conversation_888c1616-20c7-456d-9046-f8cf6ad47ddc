const _ = require("lodash");
const async = require("async");
const Joi = require("joi");
Joi.objectId = require("joi-objectid")(Jo<PERSON>);

const User = require("../../../../models/user");
const CONSTANTS = require("../../../../const");
const MESSAGES = require("../../../../message");

// Import utility function để xử lý cấu trúc phân cấp areas
const { processAreasHierarchy } = require('../../../../util/areasHierarchy');
const escapeStringRegexp = require("escape-string-regexp");

module.exports = (req, res) => {
  const limit = _.get(req, "body.limit", 10);
  const page = _.get(req, "body.page", 0);
  const sort = _.get(req, "body.sort", 1);
  const isFilter = _.get(req, "body.isFilter");
  const textSearch = _.get(req, "body.textSearch", "");
  const active = _.get(req, "body.active", "");
  const unit = _.get(req, "body.unit", "");
  const position = _.get(req, "body.position", "");
  const gender = _.get(req, "body.gender", "");
  const excludes = _.get(req, "body.excludes", []);
  let obj = { status: 1 };
  let count = 0;
  const checkParams = (next) => {
    if (textSearch && textSearch.trim()) {
      const $regex = escapeStringRegexp(textSearch.trim());
      obj["$or"] = [
        {
          name: {
            $regex,
            $options: "i",
          },
        },
        {
          username: {
            $regex,
            $options: "i",
          },
        },
        {
          phone: {
            $regex,
            $options: "i",
          },
        },
        {
          code: {
            $regex,
            $options: "i",
          },
        },
      ];
    }

    if (isFilter) {
      if (_.isNumber(active)) {
        obj.active = active;
      }
      if (unit) {
        obj.units = unit;
      }
      if (position) {
        obj.positions = position;
      }
      if (gender) {
        obj.gender = gender;
      }
    }

    if (excludes.length) {
      obj._id = { $nin: excludes };
    }

    next();
  };

  const countUser = (next) => {
    User.countDocuments(obj)
      .lean()
      .exec((err, total) => {
        count = Math.ceil(total / limit);
        next();
      });
  };

  const listUser = (next) => {
    const skip = page * limit;
    const options = {
      limit,
      skip,
      sort: sort == 1 ? "createdAt" : "-createdAt",
    };
    User.find(obj, "-password", options)
      .populate("permissions", "name code")
      .populate({
        path: "units",
        select: "name parentPath",
        populate: {
          path: "parentPath",
          select: "name",
        },
      })
      .populate("position", "name unit")
      .populate("categories", "name icon")
      .populate("jobTypes", "name")
      .populate({
        path: "areas",
        select: "name level parent parentPath",
        populate: {
          path: "parent",
          select: "name",
        },
      })
      .populate({
        path: "groupPermissions",
        select: "name permissions",
        populate: {
          path: "permissions",
          select: "name",
        },
      })
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        // Xử lý cấu trúc phân cấp areas cho từng user
        if (results && Array.isArray(results)) {
          results.forEach(user => {
            if (user.areas && user.areas.length > 0) {
              user.areas = processAreasHierarchy(user.areas);
            }
          });
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results,
          count,
        });
      });
  };

  async.waterfall([checkParams, countUser, listUser], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
